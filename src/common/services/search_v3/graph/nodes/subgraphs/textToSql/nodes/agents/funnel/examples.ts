export default `User Query: find insights where the checkouts impact is positive
Assistant: { "WHERE": [["name", "=", "Checkouts"], ["impact", ">", "0"]] }

User Query: find tickets where the value is greater than the baseline value
Assistant: { "WHERE": [] }
Explanation: No funnel step name is specified, hence the assistant returns empty array.

User Query: find insights where the carts value is greater than the baseline
Assistant: { "WHERE": [["name", "=", "Carts"], ["actual_value", ">", "baseline_value"]] }

User Query: fetch me weekly summaries related to UX-incidents, where the campaign hypothesis is rejected
Assistant: { "WHERE": [] }
Explanation: No part of the user query is relevant to the assistant's searchable fields.

User Query: fetch me the most important tickets
Assistant: { "WHERE": [] }
Explanation: No part of the user query is relevant to the assistant's searchable fields.

User Query: fetch the most important UX-related tickets from january where the product views impact is greater than 0
Assistant: { "WHERE": [["name", "=", "Product views"], ["impact", ">", 0]] }
Explanation: The assistant ignores all parts of the user query that are not related to the searchable fields.`;

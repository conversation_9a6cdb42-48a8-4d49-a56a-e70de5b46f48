export default `User Query: fetch me tickets from January 2025
Assistant: { "WHERE": [["incident_time_stamp", ">=", "2025-01-01"], ["incident_time_stamp", "<", "2025-02-01"]] }

User Query: fetch tickets from january 1
Assistant: { "WHERE": [["incident_time_stamp", ">=", "2025-01-01"], ["incident_time_stamp", "<", "2025-01-02"]] }

User Query: fetch me weekly summaries
Assistant: { "WHERE": [["is_summary", "=", "true"], ["granularity", "=", "weekly"]] }

User Query: fetch me the most important tickets
Assistant: { "WHERE": [["sensitivity", "=", "low"]] }

User Query: fetch me UX-related incidents
Assistant: { "WHERE": [] }
Explanation: None of the fields above references anything UX-related.

User Query: fetch me yearly tickets
Assistant: { "WHERE": [] }
Explanation: there is no yearly granularity.

User Query: show me the top 3 tickest for the month of Feb 2025 where the top contributor is email and are ux-related
Assistant: { "WHERE": [["incident_time_stamp", ">=", "2025-02-01"], ["incident_time_stamp", "<", "2025-03-01"]] }
Explanation: The user only needs tickets for a specific date range. The assistant can filter that, but ignores the rest of the user query.`;

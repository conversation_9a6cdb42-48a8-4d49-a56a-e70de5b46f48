import '@/../test/tools/useRedis.js';
import { jest } from '@jest/globals';
import { mockInvoke } from '@/../test/mocks/groq.js';
import { InputAnnotation } from '../../annotations.js';

describe('basicFiltering', () => {
  /* reset call‑count & one‑off implementations */
  beforeEach(() => jest.clearAllMocks());

  it('calls ChatGroq, prefixes WHERE tuples and returns partials', async () => {
    mockInvoke.mockResolvedValueOnce({
      toJSON() {
        return {
          kwargs: {
            content: JSON.stringify({ WHERE: [['title', '=', 'foo']] }),
          },
        };
      },
    });

    const { default: basicFiltering } = await import('./basicFiltering.js');

    const result = await basicFiltering(
      { userQuery: 'title = "foo"', orgId: 'org123' } as typeof InputAnnotation.State,
      { callbacks: [] },
    );

    expect(result).toEqual({ partials: { WHERE: [['i.title', '=', 'foo']] } });
    expect(mockInvoke).toHaveBeenCalledTimes(1);
  });

  it('handles empty llm response', async () => {
    mockInvoke.mockResolvedValueOnce({
      toJSON() {
        return {
          kwargs: {
            content: JSON.stringify({ WHERE: [] }),
          },
        };
      },
    });

    const { default: basicFiltering } = await import('./basicFiltering.js');

    const result = await basicFiltering(
      { userQuery: 'title = "bar"', orgId: 'org123' } as typeof InputAnnotation.State,
      { callbacks: [] },
    );

    expect(result).toEqual({ partials: { WHERE: [] } });
    expect(mockInvoke).toHaveBeenCalledTimes(1);
  });
});

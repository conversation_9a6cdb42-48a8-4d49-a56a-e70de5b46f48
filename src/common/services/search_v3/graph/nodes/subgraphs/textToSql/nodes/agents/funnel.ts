import { JoinTuple, Partials, WhereTuple } from '../../../../../../types.js';
import { InputAnnotation } from '../../annotations.js';
import { ChatGroq } from '@langchain/groq';
import systemPrompt from './funnel/systemPrompt.js';
import { withCache } from '@/common/utils/redis.js';
import { getCacheSignature } from '@/common/services/search_v3/graph/caching.js';
import { SerializedConstructor } from '@langchain/core/load/serializable';
import logger from '@/common/utils/logger.js';
import config from '@/common/services/search_v3/config.js';

const AGENT_NAME = 'funnel';

const model = new ChatGroq({
  model: config.DEFAULT_MODEL,
  temperature: 0,
}).bind({
  response_format: { type: 'json_object' },
});

const funnel = async ({
  userQuery,
  orgId,
}: typeof InputAnnotation.State): Promise<{ partials: Partials }> => {
  const messages = [
    { role: 'system', content: systemPrompt(orgId) },
    { role: 'user', content: userQuery },
  ];

  const response = await withCache(
    getCacheSignature('agent-call', [AGENT_NAME, orgId, userQuery]),
    async () => (await model.invoke(messages)).toJSON() as SerializedConstructor,
    { expiration: 60 * 60 * 1 },
  );

  // extract LLM response content
  const funnelResContent = JSON.parse(response.kwargs.content);
  // add f. prefix to WHERE conditions
  const WHERE: WhereTuple[] = (funnelResContent?.WHERE || []).map((t: WhereTuple) => [
    'f.' + t[0],
    t[1],
    t[2],
  ]);
  // add JOIN condition
  const JOIN: JoinTuple[] = WHERE.length
    ? [['insights_search_funnel as f', 'f.insightId', 'i.insightId']]
    : [];

  const funnel = { WHERE, JOIN };

  logger.info('Funnel node result', { funnel });

  return {
    partials: funnel,
  };
};

export default funnel;

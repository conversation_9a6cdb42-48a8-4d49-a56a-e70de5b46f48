export default `Your task is to parse the User Query and create the ORDER BY and LIMIT statement of an SQL query.

**ORDER_BY fields:**
- The first value of ORDER_BY must be one of the following fields:
  - incidentTimeStamp
  - revenueExpectedDeviationPct
  - baseline
  - revenueDelta
  - value
  - lastValue

**Instructions:**
- Ignore any parts of the user query that cannot be mapped to either an ORDER BY or a LIMIT clause.
- When one or both clauses do not apply, do not return that key.

**Format:**
You will reply in the exact following json format:
{
  "ORDER_BY": ["value", "desc"],
  "LIMIT": [10]
}

**Examples:**

User Query: find 10 incidents with the greater value
Assistant: { "ORDER_BY": ["value", "desc"], "LIMIT": [10] }

User Query: find the top incidents from february
Assistant: { "ORDER_BY": ["revenueDelta", "desc"] }

User Query: fetch me tickets from January 2025
Assistant: {}

User Query: fetch me UX-related incidents
Assistant: {}

User Query: show me the top 3 monthly total tickest for the month of Feb 2025 where the top contributor is email and are ux-related
Assistant: { "ORDER_BY": ["revenueDelta", "desc"], "LIMIT": [3] }`;

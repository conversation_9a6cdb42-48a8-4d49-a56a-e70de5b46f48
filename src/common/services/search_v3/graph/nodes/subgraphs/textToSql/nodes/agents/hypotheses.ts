import { JoinTuple, Partials, WhereTuple } from '../../../../../../types.js';
import { InputAnnotation } from '../../annotations.js';
import { ChatGroq } from '@langchain/groq';
import systemPrompt from './hypotheses/systemPrompt.js';
import { withCache } from '@/common/utils/redis.js';
import { LangGraphRunnableConfig } from '@langchain/langgraph';
import { getCacheSignature } from '@/common/services/search_v3/graph/caching.js';
import { SerializedConstructor } from '@langchain/core/load/serializable';
import logger from '@/common/utils/logger.js';
import config from '@/common/services/search_v3/config.js';

const AGENT_NAME = 'hypotheses';

const model = new ChatGroq({
  model: config.DEFAULT_MODEL,
  temperature: 0,
}).bind({
  response_format: { type: 'json_object' },
});

const hypotheses = async (
  { userQuery, orgId }: typeof InputAnnotation.State,
  { callbacks }: LangGraphRunnableConfig,
): Promise<{ partials: Partials }> => {
  const messages = [
    { role: 'system', content: systemPrompt(orgId) },
    { role: 'user', content: userQuery },
  ];

  const response = await withCache(
    getCacheSignature('agent-call', [AGENT_NAME, orgId, userQuery]),
    async () => (await model.invoke(messages, { callbacks })).toJSON() as SerializedConstructor,
    { expiration: 60 * 60 * 1 },
  );

  // extract LLM response content
  const hypothesesResContent = JSON.parse(response.kwargs.content);
  // add h. prefix to WHERE conditions
  const WHERE: WhereTuple[] = (hypothesesResContent?.WHERE || []).map((t: WhereTuple) => [
    'h.' + t[0],
    t[1],
    t[2],
  ]);
  // add JOIN condition
  const JOIN: JoinTuple[] = WHERE.length
    ? [['insights_search_hypotheses as h', 'h.insightId', 'i.insightId']]
    : [];

  const hypotheses = { WHERE, JOIN };

  logger.info('Hypotheses node result', { hypotheses });

  return {
    partials: hypotheses,
  };
};

export default hypotheses;

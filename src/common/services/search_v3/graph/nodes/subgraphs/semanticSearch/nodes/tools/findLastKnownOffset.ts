import { getOffsetSignature } from '../../../../../caching.js';
import { InputAnnotation } from '../../annotations.js';
import * as redis from '@/common/utils/redis.js';
import logger from '@/common/utils/logger.js';
import _ from 'lodash';

async function getCachedOffset(redisKey: string): Promise<number | undefined> {
  const cachedResult = await redis.get<number>(redisKey);
  if (!_.isNull(cachedResult)) {
    logger.info('Pool offset cache hit', { redisKey, cachedResult });
    const parsedResult = cachedResult;
    return isNaN(parsedResult) ? undefined : parsedResult;
  }
  return undefined;
}

type FindLastKnownOffset = ({ page, orgId, partials }: typeof InputAnnotation.State) => Promise<{
  lastKnownOffset: number;
  lastKnownOffsetPage: number;
}>;

const findLastKnownOffset: FindLastKnownOffset = async ({ page, orgId, partials }) => {
  let lastKnownOffset;
  let lastKnownOffsetPage = page;

  while (lastKnownOffset === undefined && lastKnownOffsetPage > 1) {
    const cacheKey = getOffsetSignature(orgId, page, partials);
    lastKnownOffset = await getCachedOffset(cacheKey);
    if (lastKnownOffsetPage === 1 || lastKnownOffset !== undefined) break;
    lastKnownOffsetPage--;
  }

  const result = {
    lastKnownOffset: lastKnownOffset ?? 0,
    lastKnownOffsetPage,
  };
  logger.info(`Found last known offset for page ${lastKnownOffsetPage}`, result);

  return result;
};

const langgraphWrapper = (state: typeof InputAnnotation.State) => findLastKnownOffset(state);

export { findLastKnownOffset };
export default langgraphWrapper;

import { kysely } from '@/common/utils/pgConn.js';
import logger from '@/common/utils/logger.js';
import { StateAnnotation } from '../../annotations.js';
import config from '../../../../../../config.js';

async function getOnePage({
  baseQuery,
  lastKnownOffset,
}: typeof StateAnnotation.State): Promise<{ candidates: Record<string, unknown>[] }> {
  const candidatesQuery = kysely
    .with('min_distances', () => baseQuery)
    .selectFrom('min_distances')
    .selectAll()
    .orderBy('distance', 'asc')
    .orderBy('incidentTimeStamp', 'desc')
    .orderBy('insightId', 'asc')
    .offset(lastKnownOffset)
    .limit(config.POOL_SIZE);

  logger.info('Sql query candidates', {
    sql: candidatesQuery.compile().sql,
    parameters: candidatesQuery.compile().parameters,
  });

  const candidates = await candidatesQuery.execute();
  logger.info('Sql query candidates result', {
    candidates: candidates.map(r => r.insightId),
  });
  return { candidates };
}

export default getOnePage;

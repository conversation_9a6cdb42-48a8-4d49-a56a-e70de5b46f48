import { Command } from '@langchain/langgraph';
import { StateAnnotation } from '../../annotations.js';
import logger from '@/common/utils/logger.js';

const isFinished = ({ page, lastKnownOffsetPage }: typeof StateAnnotation.State) => {
  logger.info('isFinished', { page, lastKnownOffsetPage });

  if (page === lastKnownOffsetPage) {
    logger.info('Found the correct page');
    return new Command({
      goto: 'get_count',
    });
  }

  logger.info('Reiterating subgraph with page', { page: lastKnownOffsetPage + 1 });
  return new Command({
    update: {
      lastKnownOffsetPage: ++lastKnownOffsetPage,
    },
    goto: 'get_one_page',
  });
};

export default isFinished;

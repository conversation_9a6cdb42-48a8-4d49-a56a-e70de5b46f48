import { END, START, StateGraph } from '@langchain/langgraph';

import { StateAnnotation, InputAnnotation, OutputAnnotation } from './annotations.js';
import resultsEvaluator from './nodes/agents/resultsEvaluator.js';
import isFinished from './nodes/commands/isFinished.js';
import findLastKnownOffset from './nodes/tools/findLastKnownOffset.js';
import getOnePage from './nodes/tools/getOnePage.js';
import getCount from './nodes/tools/getCount.js';

const workflow = new StateGraph({
  input: InputAnnotation,
  output: OutputAnnotation,
  stateSchema: StateAnnotation,
})
  .addNode('find_last_known_offset', findLastKnownOffset)
  .addNode('get_one_page', getOnePage)
  .addNode('results_evaluator', resultsEvaluator)
  .addNode('get_count', getCount)
  .addNode('is_finished', isFinished, { ends: ['get_one_page', 'get_count'] })
  .addEdge(START, 'find_last_known_offset')
  .addEdge('find_last_known_offset', 'get_one_page')
  .addEdge('get_one_page', 'results_evaluator')
  .addEdge('results_evaluator', 'is_finished')
  .addEdge('get_count', END);

const graph = workflow.compile();

export default graph;

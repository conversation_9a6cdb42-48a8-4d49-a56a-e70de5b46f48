import {
  Partials,
  SearchCompiledQuery,
  SemanticQuery,
  SearchResult,
} from '@/common/services/search_v3/types.js';
import { TicketPool } from '@/types.js';
import { Annotation } from '@langchain/langgraph';

const InputAnnotation = Annotation.Root({
  page: Annotation<number>,
  orgId: Annotation<string>,
  partials: Annotation<Partials>,
  semanticQuery: Annotation<SemanticQuery>,
  baseQuery: Annotation<SearchCompiledQuery>,
  x: Annotation<boolean>,
});

const OutputAnnotation = Annotation.Root({
  result: Annotation<SearchResult>,
});

const StateAnnotation = Annotation.Root({
  page: Annotation<number>,
  orgId: Annotation<string>,
  partials: Annotation<Partials>,
  semanticQuery: Annotation<SemanticQuery>,
  baseQuery: Annotation<SearchCompiledQuery>,
  lastKnownOffset: Annotation<number>,
  lastKnownOffsetPage: Annotation<number>,
  candidates: Annotation<TicketPool[]>,
  insightIds: Annotation<number[]>,
  count: Annotation<number>,
  result: Annotation<SearchResult>,
});

export { StateAnnotation, InputAnnotation, OutputAnnotation };

import { StateAnnotation } from '../../annotations.js';
import { SearchResult } from '../../../types.js';
import { Insight } from '@/types.js';
import config from '../../../config.js';
import _ from 'lodash';
import logger from '@/common/utils/logger.js';

const nonSemanticSearch = async ({
  page,
  baseQuery,
  partials,
}: typeof StateAnnotation.State): Promise<{ result: SearchResult }> => {
  const offset = (page - 1) * config.PAGE_SIZE;

  const pageLimit = partials.LIMIT
    ? _.clamp(partials.LIMIT[0] - offset, 0, config.PAGE_SIZE)
    : config.PAGE_SIZE;

  const [column, direction] = partials.ORDER_BY || ['i.incidentTimeStamp', 'desc'];

  const insightsQuery = baseQuery
    .orderBy(column, direction)
    .orderBy('i.insightId', 'desc')
    .offset(offset)
    .limit(pageLimit);

  const filteredCountQuery = baseQuery
    .clearSelect()
    .select(({ fn }) => [fn.count<number>('i.insightId').as('count')]);

  logger.info('Running non-semantic SQL query', {
    insightsQuery: {
      sql: insightsQuery.compile().sql,
      params: insightsQuery.compile().parameters,
    },
    filteredCountQuery: {
      sql: filteredCountQuery.compile().sql,
      params: filteredCountQuery.compile().parameters,
    },
  });

  const [insightsViewResult, filteredCountResult] = await Promise.all([
    insightsQuery.execute(),
    partials.LIMIT ? Promise.resolve([{ count: partials.LIMIT[0] }]) : filteredCountQuery.execute(),
  ]);

  // manually to camelCase because the insights_search view returns the insight
  // inside a new json field called insight_json
  const insights = insightsViewResult
    .map(i => i.insightJson as Record<string, unknown>)
    .map(iJson => _.mapKeys(iJson, (_v, k) => _.camelCase(k))) as Insight[];

  const filteredCount = filteredCountResult[0].count;

  return {
    result: {
      insights,
      filteredCount,
    },
  };
};

export default nonSemanticSearch;

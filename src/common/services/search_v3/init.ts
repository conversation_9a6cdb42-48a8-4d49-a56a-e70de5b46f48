import { kysely } from '@/common/utils/pgConn.js';
import { sql } from 'kysely';
import _ from 'lodash';

const getDistinctValuesPerOrg = (column: string, field: string) =>
  kysely
    .selectFrom(
      kysely
        .selectFrom('insights')
        .select([
          'orgId',
          sql`jsonb_array_elements(${sql.raw(`"${column}"`)}::jsonb)->>${sql.raw(`'${field}'`)}`.as(
            'value',
          ),
        ])
        .as('subquery'),
    )
    .select(['orgId', kysely.fn.agg('array_agg', [sql`DISTINCT value`]).as('values')])
    .groupBy('orgId')
    .execute();

const funnelNameEnumsProm = getDistinctValuesPerOrg('funnel_metrics', 'name');
const hypothesisIdEnumsProm = getDistinctValuesPerOrg('hypotheses', 'id');
const hypothesisInspectorEnumsProm = getDistinctValuesPerOrg('hypotheses', 'inspector');

const [funnelNameEnums, hypothesisIdEnums, hypothesisInspectorEnums] = await Promise.all([
  funnelNameEnumsProm,
  hypothesisIdEnumsProm,
  hypothesisInspectorEnumsProm,
]);

const flatten = (x: { orgId: unknown; values: unknown }[]) =>
  _(x)
    .groupBy('orgId')
    .mapValues(group => group.flatMap(v => v.values).filter(x => x))
    .value();

export const enums = {
  funnel: {
    names: flatten(funnelNameEnums),
  },
  hypotheses: {
    ids: flatten(hypothesisIdEnums),
    inspectors: flatten(hypothesisInspectorEnums),
  },
};

import { StateGraph, START, END } from '@langchain/langgraph';

import { StateAnnotation, InputAnnotation, OutputAnnotation } from './annotations.js';
import baseQueryBuilder from './nodes/tools/baseQueryBuilder.js';
import isSemantic from './nodes/conditionals/isSemantic.js';
import semanticSearch from './nodes/subgraphs/semanticSearch.js';
import textToSql from './nodes/subgraphs/textToSql.js';
import nonSemanticFetch from './nodes/tools/nonSemanticFetch.js';

const workflow = new StateGraph({
  input: InputAnnotation,
  output: OutputAnnotation,
  stateSchema: StateAnnotation,
})
  .addNode('text_to_sql', textToSql)
  .addNode('base_query_builder', baseQueryBuilder)
  .addNode('semantic_search', semanticSearch)
  .addNode('non_semantic_fetch', nonSemanticFetch)
  .addEdge(START, 'text_to_sql')
  .addEdge('text_to_sql', 'base_query_builder')
  .addConditionalEdges('base_query_builder', isSemantic, {
    true: 'semantic_search',
    false: 'non_semantic_fetch',
  })
  .addEdge('semantic_search', END)
  .addEdge('non_semantic_fetch', END);

const graph = workflow.compile();

export default graph;

import { Partials, WhereTuple } from '../../../types.js';
import { InputAnnotation } from '../../annotations.js';
import { ChatGroq } from '@langchain/groq';
import systemPrompt from './textToSql/systemPrompt.js';
import { LangGraphRunnableConfig } from '@langchain/langgraph';
import { SerializedConstructor } from '@langchain/core/load/serializable';
import logger from '@/common/utils/logger.js';
import config from '@/common/services/search_v3/config.js';

const model = new ChatGroq({
  model: config.DEFAULT_MODEL,
  temperature: 0,
}).bind({
  response_format: { type: 'json_object' },
});

const textToSql = async (
  { userQuery, orgId }: typeof InputAnnotation.State,
  { callbacks }: LangGraphRunnableConfig,
): Promise<{ partials: Partials }> => {
  const messages = [
    { role: 'system', content: systemPrompt(new Date().toISOString().split('T')[0], orgId) },
    { role: 'user', content: userQuery },
  ];

  const response = (await model.invoke(messages, { callbacks })).toJSON() as SerializedConstructor;

  // extract LLM response content
  const basicFiltering = JSON.parse(response.kwargs.content);
  // add i. prefix to WHERE conditions
  basicFiltering.WHERE = (basicFiltering?.WHERE || []).map((t: WhereTuple) => [
    'i.' + t[0],
    t[1],
    t[2],
  ]);

  logger.info('basic_filtering node result', { basicFiltering });

  return {
    partials: basicFiltering,
  };
};

export default textToSql;

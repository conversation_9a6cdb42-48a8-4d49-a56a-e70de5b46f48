import logger from '@/common/utils/logger.js';
import { Partials, SemanticQuery } from '../../../types.js';
import { InputAnnotation as ParentStateAnnotation } from '../../annotations.js';
import subgraph from './textToSql/graph.js';

const textToSql = async ({
  userQuery,
  page,
  orgId,
}: typeof ParentStateAnnotation.State): Promise<{
  partials: Partials;
  semanticQuery: SemanticQuery;
}> => {
  logger.info('Running text-to-sql subgraph', { userQuery, page, orgId });
  return await subgraph.invoke({ userQuery, page, orgId });
};

export default textToSql;

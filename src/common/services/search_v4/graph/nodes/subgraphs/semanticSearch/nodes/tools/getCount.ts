import * as redis from '@/common/utils/redis.js';
import config from '../../../../../../config.js';
import { getTotalCountSignature } from '../../../../../caching.js';
import _ from 'lodash';
import { StateAnnotation } from '../../annotations.js';
import logger from '@/common/utils/logger.js';
import { SearchResult } from '@/common/services/search_v3/types.js';

const calculateTotalCount = (page: number, resultLength: number): number =>
  (page - 1) * config.PAGE_SIZE + resultLength;

async function getTotalCountNumber(
  totalCountSignature: string,
  page: number,
  resultLength: number,
): Promise<number> {
  const cachedTotalCount = await redis.get<number>(totalCountSignature);
  if (!_.isNull(cachedTotalCount)) return cachedTotalCount;
  if (resultLength === config.PAGE_SIZE) return -1; // is not cached, cannot calculate (is not last page)

  const totalCount = calculateTotalCount(page, resultLength); // is not cached, can calculate (is last page)
  await redis.set(totalCountSignature, totalCount, 8 * 60 * 60);
  return totalCount;
}

async function getTotalCount({
  orgId,
  page,
  partials,
  result: { insights },
}: typeof StateAnnotation.State): Promise<{ result: SearchResult }> {
  const insightsCount = await getTotalCountNumber(
    getTotalCountSignature(orgId, partials),
    page,
    insights.length,
  );

  logger.info('Total count', { insightsCount });

  return { result: { insights, filteredCount: insightsCount } };
}

export default getTotalCount;

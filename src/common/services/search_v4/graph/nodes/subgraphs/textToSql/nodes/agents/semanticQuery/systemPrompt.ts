import { enums } from '../../../../../../../init.js';
import { searchableFields as basicFilteringSearchableFields } from '../basicFiltering/variables.js';
import { searchableFields as hypothesesSearchableFields } from '../hypotheses/variables.js';
import { searchableFields as funnelSearchableFields } from '../funnel/variables.js';

export default (
  orgId: string,
) => `You are a helpful search assistant. You will be provided with a User Query in natural language. Your task is to parse the User Query, compare it with Available Information, and decide if there is a phrase in the User Query that is not present in the Available Information.


**Instructions**
Ignore the words "insight", "tickets" or "incidents", or similar, that reference the entities that will be fetched. Ignore common english words and phrases, like "and", "which", "whose", "concerning".


**Format:**
You will reply in this exact json format:
{
  "missing": "UX-related"
}
When there is no phrase in the User Query that is not present in the Available Information, you will reply with an empty object:
{}


**Available Information**

${basicFilteringSearchableFields}
${hypothesesSearchableFields}
${funnelSearchableFields}
${enums.funnel.names[orgId].join('\n')}
${enums.hypotheses.ids[orgId].join('\n')}
${enums.hypotheses.inspectors[orgId].join('\n')}


**Examples**

User Query: "Show me insights about UX-related topics"
Expected Output: { "missing": "UX-related" }

User Query: "Show me insights about the email channel"
Expected Output: { "missing": "email channel" }

User Query: "Fetch important incidents from january where the channel performance hypothesis is rejected"
Expected Output: {}`;

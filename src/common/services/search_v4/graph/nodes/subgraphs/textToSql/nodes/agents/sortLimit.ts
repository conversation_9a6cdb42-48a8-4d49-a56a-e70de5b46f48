import { Partials } from '../../../../../../types.js';
import { InputAnnotation } from '../../annotations.js';
import { ChatGroq } from '@langchain/groq';
import systemPrompt from './sortLimit/systemPrompt.js';
import { withCache } from '@/common/utils/redis.js';
import { LangGraphRunnableConfig } from '@langchain/langgraph';
import { getCacheSignature } from '@/common/services/search_v3/graph/caching.js';
import { SerializedConstructor } from '@langchain/core/load/serializable';
import logger from '@/common/utils/logger.js';
import config from '@/common/services/search_v3/config.js';

const AGENT_NAME = 'sort_limit';

const model = new ChatGroq({
  model: config.DEFAULT_MODEL,
  temperature: 0,
}).bind({
  response_format: { type: 'json_object' },
});

const sortLimit = async (
  { userQuery, orgId }: typeof InputAnnotation.State,
  { callbacks }: LangGraphRunnableConfig,
): Promise<{ partials: Partials }> => {
  const messages = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userQuery },
  ];

  const response = await withCache(
    getCacheSignature('agent-call', [AGENT_NAME, orgId, userQuery]),
    async () => (await model.invoke(messages, { callbacks })).toJSON() as SerializedConstructor,
    { expiration: 60 * 60 * 1 },
  );

  // extract LLM response content
  const sortLimit = JSON.parse(response.kwargs.content);
  // add alias
  if (sortLimit.ORDER_BY?.length) {
    sortLimit.ORDER_BY[0] = 'i.' + sortLimit.ORDER_BY[0];
  }

  logger.info('sort_limit node result', { sortLimit });

  return {
    partials: sortLimit,
  };
};

export default sortLimit;

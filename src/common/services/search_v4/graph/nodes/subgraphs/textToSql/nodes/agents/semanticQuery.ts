import { InputAnnotation } from '../../annotations.js';
import { ChatGroq } from '@langchain/groq';
import systemPrompt from './semanticQuery/systemPrompt.js';
import { withCache } from '@/common/utils/redis.js';
import { LangGraphRunnableConfig } from '@langchain/langgraph';
import { getCacheSignature } from '@/common/services/search_v3/graph/caching.js';
import { SerializedConstructor } from '@langchain/core/load/serializable';
import logger from '@/common/utils/logger.js';
import { generateEmbedding } from '@/common/services/openAIService.js';
import { JoinTuple, Partials, SemanticQuery } from '../../../../../../types.js';
import { sql } from 'kysely';
import config from '@/common/services/search_v3/config.js';

const AGENT_NAME = 'semanticQuery';

const model = new ChatGroq({
  model: config.DEFAULT_MODEL,
  temperature: 0,
  maxTokens: undefined,
}).bind({
  response_format: { type: 'json_object' },
});

export default async (
  { userQuery, orgId }: typeof InputAnnotation.State,
  { callbacks }: LangGraphRunnableConfig,
): Promise<{ semanticQuery: SemanticQuery | undefined; partials: Partials }> => {
  const messages = [
    { role: 'system', content: systemPrompt(orgId) },
    { role: 'user', content: userQuery },
  ];

  const response = await withCache(
    getCacheSignature('agent-call', [AGENT_NAME, userQuery]),
    async () => (await model.invoke(messages, { callbacks })).toJSON() as SerializedConstructor,
    { expiration: 60 * 60 * 1 },
  );

  const text = JSON.parse(response.kwargs.content)?.missing;

  let semanticQuery, JOIN: JoinTuple[] | undefined, SELECT, DISTINCT_ON;
  if (text) {
    const embeddingRes = await generateEmbedding(text);
    semanticQuery = {
      text,
      embedding: (embeddingRes?.data as Array<{ embedding: number[] }>)[0].embedding,
    };
    JOIN = [
      ['revenue_ticket as r', 'r.insightId', 'i.insightId'],
      ['revenue_ticket_embedding as e', 'e.revenue_ticket_id', 'r.id'],
    ];
    SELECT = [
      sql`${sql.ref('e.embedding')} <-> ${sql.lit(JSON.stringify(semanticQuery.embedding))}`.as(
        'distance',
      ),
      'e.textValue',
    ];
    DISTINCT_ON = ['e.revenueTicketId'];
  }

  logger.info('semanticQuery node result', { semanticQuery, JOIN, SELECT, DISTINCT_ON });

  return {
    semanticQuery,
    partials: {
      JOIN,
      SELECT,
      DISTINCT_ON,
    },
  };
};

import { StateGraph, START, END } from '@langchain/langgraph';

import { StateAnnotation, InputAnnotation, OutputAnnotation } from './annotations.js';
import basicFiltering from './nodes/agents/basicFiltering.js';
import funnel from './nodes/agents/funnel.js';
import hypotheses from './nodes/agents/hypotheses.js';
import semanticQuery from './nodes/agents/semanticQuery.js';
import sortLimit from './nodes/agents/sortLimit.js';

const workflow = new StateGraph({
  input: InputAnnotation,
  output: OutputAnnotation,
  stateSchema: StateAnnotation,
})
  .addNode('basic_filtering', basicFiltering)
  .addNode('funnel', funnel)
  .addNode('hypotheses', hypotheses)
  .addNode('semantic_query', semanticQuery)
  .addNode('sort_limit', sortLimit)
  .addEdge(START, 'basic_filtering')
  .addEdge(START, 'funnel')
  .addEdge(START, 'hypotheses')
  .addEdge(START, 'semantic_query')
  .addEdge(START, 'sort_limit')
  .addEdge('basic_filtering', END)
  .addEdge('funnel', END)
  .addEdge('hypotheses', END)
  .addEdge('semantic_query', END)
  .addEdge('sort_limit', END);

const graph = workflow.compile();

export default graph;

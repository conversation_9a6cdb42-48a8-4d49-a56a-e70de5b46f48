export default `User Query: fetch me tickets where the channel performance hypothesis is retained
Assistant: { "WHERE": [["hypothesis_id", "=", "channel-performance-differences"], ["verdict", "=", "retained"]] }

User Query: fetch me incidents from the web analytics inspector
Assistant: { "WHERE": [["inspector", "=", "web_analytics"]] }

User Query: find insights where the competitors news is not started
Assistant: { "WHERE": [["hypothesis_id", "=", "competitors-news"], ["state", "=", "not-started"]] }

User Query: fetch me weekly summaries related to UX-incidents, where the campaign hypothesis is rejected
Assistant: { "WHERE": [["hypothesis_id", "=", "campaign-performance-differences"], ["verdict", "=", "rejected"]] }
Explanation: The only part of the user query that is searchable by the assistant is "campaign hypothesis is rejected", hence anything else is ignored.

User Query: fetch me the most important tickets
Assistant: { "WHERE": [] }
Explanation: No information related to the searchable fields is present in the user query, hence the assistant returns an empty array

User Query: tickets from january
Assistant: { "WHERE": [] }
Explanation: The assistant has no date-related information`;

export const searchableFields: string = `- incident_time_stamp (DATE) // query this for anything date-related
- granularity ('daily'|'weekly'|'monthly')
- revenue_expected_deviation_pct (NUMERIC)
- baseline (NUMERIC)
- revenue_delta (NUMERIC)
- anomaly_detection_mode ('RP'|'TCA')
- sensitivity ('low'|'medium'|'high') // set sensitivity 'low' to only return the important tickets
- is_total (BOOLEAN)
- is_summary (BOOLEAN)
- currency ('USD'|'EUR')
- value (NUMERIC)
- last_value (NUMERIC)`;

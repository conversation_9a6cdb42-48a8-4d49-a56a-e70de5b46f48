import app from './http/index.js';
import * as pgConn from './common/utils/pgConn.js';
import * as redis from './common/utils/redis.js';
import './common/services/search_v3/init.js';

// see https://github.com/aws-samples/graceful-shutdown-with-aws-lambda
process.on('SIGTERM', pgConn.gracefullyShutdown);
process.on('SIGINT', pgConn.gracefullyShutdown);

void redis.client.connect();

const PORT = process.env.PORT || 3002;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

const config = {
  preset: 'ts-jest',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  transform: {
    '^.+\\.ts$': [
      'ts-jest',
      {
        tsconfig: 'tsconfig.test.json',
        useESM: true,
        allowJs: true,
        moduleResolution: 'NodeNext',
      },
    ],
  },
  testMatch: ['**/test/**/*.[jt]s?(x)', '**/?(*.)+(spec|test).[jt]s?(x)'],
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/test/resources/',
    '/test/tools/',
    '/test/mocks/',
    '/migrations/',
  ],
  roots: ['./src', './test'],
  testEnvironment: 'node',
  globalSetup: './jest.setup.mjs',
  globalTeardown: './jest.teardown.mjs',
  extensionsToTreatAsEsm: ['.ts'],
  injectGlobals: true,
  resolver: 'ts-jest-resolver',
  testRunner: 'jest-circus/runner',
  setupFilesAfterEnv: ['<rootDir>/jest.env.mjs'],
  moduleDirectories: ['node_modules', 'src'],
  // TODO Remove after properly fixing tests that currently error out when ran in parallel
  maxWorkers: 1,
  setupFiles: ['<rootDir>/test/mocks/groq.ts'],
};

export default config;
